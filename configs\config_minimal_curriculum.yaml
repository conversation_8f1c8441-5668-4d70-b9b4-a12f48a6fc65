# 极简模型 + 课程学习训练配置
# 结合轻量级架构和智能训练策略

# === 核心训练参数 ===
data_dir: "data"
output_dir: "runs/train_minimal_curriculum"
nc: 5
img_size: 640
batch_size: 8
epochs: 200
num_workers: 0
grad_clip: 5.0

# === 极简模型配置 ===
model:
  type: "minimal"
  fusion_type: "minimal"
  shared_backbone: true
  single_scale: true

# === 课程学习配置 ===
curriculum_learning: true

# 类别样本统计（基于实际数据分析）
class_samples:
  0: 180  # 110_two_hight_glass (32.5%)
  1: 158  # Glass_Dirty (28.6%)
  2: 38   # Glass_Loss (6.9%)
  3: 148  # Polyme_Dirty (26.8%)
  4: 29   # insulator (5.2%)

# 类别难度分级（基于样本数量）
class_difficulty:
  0: "easy"    # 110_two_hight_glass (样本最多)
  1: "medium"  # Glass_Dirty (样本中等)
  2: "hard"    # Glass_Loss (样本少)
  3: "medium"  # Polyme_Dirty (样本中等)
  4: "hard"    # insulator (样本最少)

# 基础类别权重（会被课程学习动态调整）
class_weights: [1.0, 3.0, 10.0, 4.0, 15.0]

# === 课程学习阶段策略 ===
curriculum_stages:
  stage1:  # 0-25% 轮次
    name: "简单类别为主"
    easy_weight: 1.0
    medium_weight: 0.3
    hard_weight: 0.1
    description: "主要训练样本充足的类别，建立基础特征"
    
  stage2:  # 25-50% 轮次
    name: "引入中等难度"
    easy_weight: 1.0
    medium_weight: 0.8
    hard_weight: 0.3
    description: "逐步引入中等难度类别，扩展特征表示"
    
  stage3:  # 50-75% 轮次
    name: "重点困难类别"
    easy_weight: 0.8
    medium_weight: 1.0
    hard_weight: 1.5
    description: "重点训练困难类别，强化少数类别学习"
    
  stage4:  # 75-100% 轮次
    name: "全类别平衡"
    easy_weight: 1.0
    medium_weight: 1.0
    hard_weight: 1.0
    description: "全类别平衡训练，巩固所有知识"

# === 优化器配置 ===
optimizer:
  type: "Adam"
  lr: 0.001
  weight_decay: 0.0001

# === 学习率调度 ===
scheduler:
  type: "CosineAnnealingLR"
  T_max: 200
  eta_min: 1e-6

# === 早停配置 ===
early_stopping:
  patience: 50  # 课程学习需要更多耐心
  min_delta: 0.001

# === Focal Loss配置 ===
loss_config:
  use_focal_loss: true
  focal_alpha: 0.25
  focal_gamma: 2.0

# === 损失函数权重 ===
loss_weights:
  box_loss: 1.0
  obj_loss: 4.0
  cls_loss: 3.0

# === 评估配置 ===
evaluation:
  conf_thresh: 0.01
  iou_thresh: 0.5
  save_best: true

# === 强化数据增强 ===
augmentation:
  brightness_contrast: 0.5
  blur_prob: 0.3
  flip_prob: 0.5
  color_jitter: 0.4
  rotate_prob: 0.2
  scale_range: [0.6, 1.4]
  mixup_prob: 0.2
  cutmix_prob: 0.2
  mosaic_prob: 0.3

# === 高级配置 ===
use_mixed_precision: false
use_ema: true

# === 类别特定增强 ===
class_specific_augmentation:
  Glass_Loss:
    extra_augment_prob: 0.9
    copy_paste_prob: 0.5
  insulator:
    extra_augment_prob: 0.9
    copy_paste_prob: 0.5

# === 采样策略 ===
sampling_strategy:
  use_balanced_sampling: true
  oversample_minority: true
  minority_oversample_ratio: 5.0

# === 日志配置 ===
logging:
  log_interval: 5
  save_interval: 20
  class_specific_metrics: true
  curriculum_progress: true

# === 课程学习特定配置 ===
curriculum_config:
  dynamic_weighting: true
  stage_transition_smooth: true
  monitor_class_performance: true
  adaptive_difficulty: false  # 暂时关闭自适应难度

# === 训练策略说明 ===
# 阶段1 (0-50轮): 简单类别为主
#   - 110_two_hight_glass: 权重 1.0 * 1.0 = 1.0
#   - Glass_Dirty: 权重 3.0 * 0.3 = 0.9
#   - Glass_Loss: 权重 10.0 * 0.1 = 1.0
#   - Polyme_Dirty: 权重 4.0 * 0.3 = 1.2
#   - insulator: 权重 15.0 * 0.1 = 1.5

# 阶段2 (50-100轮): 引入中等难度
#   - 110_two_hight_glass: 权重 1.0 * 1.0 = 1.0
#   - Glass_Dirty: 权重 3.0 * 0.8 = 2.4
#   - Glass_Loss: 权重 10.0 * 0.3 = 3.0
#   - Polyme_Dirty: 权重 4.0 * 0.8 = 3.2
#   - insulator: 权重 15.0 * 0.3 = 4.5

# 阶段3 (100-150轮): 重点困难类别
#   - 110_two_hight_glass: 权重 1.0 * 0.8 = 0.8
#   - Glass_Dirty: 权重 3.0 * 1.0 = 3.0
#   - Glass_Loss: 权重 10.0 * 1.5 = 15.0
#   - Polyme_Dirty: 权重 4.0 * 1.0 = 4.0
#   - insulator: 权重 15.0 * 1.5 = 22.5

# 阶段4 (150-200轮): 全类别平衡
#   - 110_two_hight_glass: 权重 1.0 * 1.0 = 1.0
#   - Glass_Dirty: 权重 3.0 * 1.0 = 3.0
#   - Glass_Loss: 权重 10.0 * 1.0 = 10.0
#   - Polyme_Dirty: 权重 4.0 * 1.0 = 4.0
#   - insulator: 权重 15.0 * 1.0 = 15.0
